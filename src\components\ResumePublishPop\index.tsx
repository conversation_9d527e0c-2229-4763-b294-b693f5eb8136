import { useEffect, useState } from 'react'
import Popup from '@/components/Popup'
import { actions, dispatch, useSelector } from '@/core/store/index'
import { IDefValProps } from '@/pages/resume/data'
import ResumePublish from '@/pages/resume/components/ResumePublish'
import { savePublishData } from '@/core/utils/publish'
import useLocation from '@/hooks/useHomeLocation'
import { getLocationByApi, getAreaByAdcode } from '@/utils/location'

type IProps = {
  visible: boolean
  /** 城市ID，格式:[id,id,id] */
  hopeAreas?: Array<any>
  /** 工种，格式:[{industry: 行业, occIds: [工种ID,工种ID]}],可传空数组[] */
  occupations?: Array<any>
  onClose?: () => void
  onConfirmCbFn?: () => void
}

// 发布简历弹窗
export default (props: IProps) => {
  const { visible, hopeAreas, onClose, onConfirmCbFn } = props
  const [show, setShow] = useState(false)
  const [isShowGpsTips, setShowGpsTips] = useState(true)
  const [defVal, setDefVal] = useState<IDefValProps>({ hopeAreas: [], occupations: [] })
  const userInfo = useSelector((state) => state.storage.userInfo)

  useEffect(() => {
    if (visible) {
      initData()
      const page = $.router.getCurrentPage()

      if (page.$taroPath.indexOf('pages/index/index') >= 0) {
        const reportData = {
          name: '简历发布前置弹窗',
          page_name: '首页',
        }
        $.report.event('userPopupExposure', reportData)
      }
    }
    setShow(visible)
  }, [visible])

  useEffect(() => {
    if (userInfo.userId) {
      initData()
    }
  }, [userInfo.userId])

  // 使用新的 useLocation hook，支持回调函数
  const homeLocation = useLocation((newLocationData: any) => {
    // 当定位更新时的回调处理
    if (newLocationData?.success && newLocationData?.data?.areaId) {
      const { areaId } = newLocationData.data
      setDefVal(pre => ({ ...pre, hopeAreas: [`${areaId}`] }))
      setShowGpsTips(false)
      // 保存定位数据到发布数据中
      savePublishData('hopeAreas', [`${areaId}`])
    }
  })

  const initData = async () => {
    // 没登录不允许发布
    if (!userInfo.userId) {
      return
    }
    const params: any = {}
    try {
      if (!homeLocation.success) {
        return
      }
      const { latitude, longitude } = homeLocation?.data || {}

      if (longitude && latitude) {
        params.longitude = longitude
        params.latitude = latitude
        setShowGpsTips(false)
      }
    } catch {
      setShowGpsTips(true)
    }
    const areaId = homeLocation?.data.areaId
    let nHopeAreas: Array<string> = []
    if (areaId) {
      nHopeAreas = [`${areaId}`]
    } else if ($.isArrayVal(hopeAreas)) {
      nHopeAreas = [...(hopeAreas || [])]
    }

    setDefVal(pre => ({ ...pre, hopeAreas: nHopeAreas }))
  }

  const onHidePop = () => {
    /** 关闭埋点 */
    const reportData = {
      name: '简历发布前置弹窗',
      page_name: '首页',
    }
    $.report.event('closePopupClick', reportData)
    setShow(false)
    onClose && onClose()
  }

  /** 拉取定位授权 - 使用新的定位逻辑 */
  const onClickGps = () => {
    $.taro.getLocation({
      type: 'gcj02', // 代表获取经纬度
      success: async (res) => {
        try {
          // 使用新的定位API获取地址信息
          const { addr } = await getLocationByApi(String(res.longitude), String(res.latitude))
          const { province, city, current } = await getAreaByAdcode(addr.adcode) || {}

          const value = city || province
          const isValue = !!(value && value.id)

          if (isValue && current && current.id) {
            const locationData = {
              success: true,
              data: {
                latitude: res.latitude,
                longitude: res.longitude,
                areaId: Number(current.id),
                name: current.name,
              },
            }

            // 更新 homeLocation 存储
            dispatch(actions.storage.setItem({ key: 'homeLocation', value: locationData as any }))

            // 设置定位回显
            savePublishData('hopeAreas', [`${current.id}`])
            setDefVal(pre => ({ ...pre, hopeAreas: [`${current.id}`] }))

            // 隐藏定位提示
            setShowGpsTips(false)
          } else {
            $.msg('定位失败，请稍后再试')
          }
        } catch {
          $.msg('定位失败，请稍后再试')
        }
      },
      fail: (err: any) => {
        if (err.errMsg?.includes('auth deny') || err.errMsg?.includes('authorize no response')) {
          // 打开设置页面进行授权
          $.taro.openSetting({
            success(settingRes) {
              const { authSetting } = settingRes
              if (authSetting['scope.userLocation']) {
                // 用户同意授权，重新获取定位
                onClickGps()
              }
            },
          })
          return
        }

        if (!err || err.code !== 401) {
          $.msg('定位失败，请稍后再试')
        }
      },
    })
  }

  return (
    <Popup
      visible={show}
      position="bottom"
    >
      <ResumePublish
        value={defVal}
        isPopStyle
        isShowGpsTips={isShowGpsTips}
        cityLabel="工作城市"
        occLabel='期望职位'
        cityPlaceholder='请选择'
        occPlaceholder='请选择'
        onHidePop={onHidePop}
        onClickGps={onClickGps}
        onConfirmCbFn={onConfirmCbFn}
      />

    </Popup>
  )
}
